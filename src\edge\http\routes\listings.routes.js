const ListingController = require('../controllers/listing.controller');
const { authHook } = require('../../../hooks/auth.hook');

async function listingRoutes(fastify, options) {
  // Get container from fastify instance
  const container = fastify.container;
  const listingController = new ListingController(container);


  // Create listing
  fastify.post('/', {
    preHandlers: [authHook],
    schema: {
      tags: ['listings'],
      summary: 'Create a new listing',
      description: 'Create a new rental listing',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['CategoryID', 'Title', 'Description', 'Location', 'PricePerDay', 'ListingType'],
        properties: {
          // Basic listing fields
          CategoryID: { type: 'string', format: 'uuid' },
          SubcategoryID: { type: 'string', format: 'uuid' },
          ListingType: {
            type: 'string',
            enum: ['Property', 'Vehicle', 'Furniture', 'HomeAppliance', 'PhotographyEquipment']
          },
          Title: { type: 'string', minLength: 3, maxLength: 200 },
          Description: { type: 'string', minLength: 10, maxLength: 5000 },
          Location: { type: 'string', minLength: 3, maxLength: 200 },
          Latitude: { type: 'number', minimum: -90, maximum: 90 },
          Longitude: { type: 'number', minimum: -180, maximum: 180 },
          PricePerDay: { type: 'number', minimum: 0 },
          PricePerWeek: { type: 'number', minimum: 0 },
          PricePerMonth: { type: 'number', minimum: 0 },
          SecurityDeposit: { type: 'number', minimum: 0, default: 0 },
          Condition: {
            type: 'string',
            enum: ['new', 'good', 'used']
          },
          DeliveryOptions: {
            type: 'string',
            enum: ['pickup', 'homeDelivery']
          },

          // Property specific fields
          PropertyDetails: {
            type: 'object',
            properties: {
              PropertyType: { type: 'string' },
              Address: { type: 'string' },
              AreaSize: { type: 'number' },
              NumberOfBedrooms: { type: 'integer', minimum: 0 },
              NumberOfBathrooms: { type: 'integer', minimum: 0 },
              Furnished: { type: 'boolean' },
              FloorNumber: { type: 'integer' },
              ParkingAvailable: { type: 'boolean' },
              AvailableFrom: { type: 'string', format: 'date-time' },
              MinimumRentalPeriod: { type: 'integer', minimum: 1 }
            }
          },

          // Vehicle specific fields
          VehicleDetails: {
            type: 'object',
            properties: {
              VehicleType: { type: 'string' },
              BrandName: { type: 'string' },
              ModelName: { type: 'string' },
              RegistrationNumber: { type: 'string' },
              YearOfManufacture: { type: 'integer', minimum: 1900 },
              Color: { type: 'string'},
              NumberOfSeats: { type: 'integer', minimum: 1 }
            }
          },

          // Furniture specific fields
          FurnitureDetails: {
            type: 'object',
            properties: {
              FurnitureType: { type: 'string' },
              Material: { type: 'string' },
              Size: { type: 'string' },
              Color: { type: 'string' },
              Quantity: { type: 'integer', minimum: 1 }
            }
          },

          // Home Appliance specific fields
          HomeApplianceDetails: {
            type: 'object',
            properties: {
              ApplianceType: { type: 'string' },
              BrandName: { type: 'string' },
              ModelNumber: { type: 'string' },
              PowerRating: { type: 'string' },
              WarrantyAvailable: { type: 'boolean' }
            }
          },

          // Photography Equipment specific fields
          PhotographyEquipmentDetails: {
            type: 'object',
            properties: {
              EquipmentType: { type: 'string' },
              BrandModel: { type: 'string' },
              UsageInstructionsAttached: { type: 'boolean' },
              AccessoriesIncluded: { type: 'string' },
              PickupLocation: { type: 'string' }
            }
          }
        },
        // Conditionally require specific details based on ListingType
        if: {
          properties: { ListingType: { const: 'Property' } }
        },
        then: {
          required: ['PropertyDetails']
        },
        else: {
          if: {
            properties: { ListingType: { const: 'Vehicle' } }
          },
          then: {
            required: ['VehicleDetails']
          },
          else: {
            if: {
              properties: { ListingType: { const: 'Furniture' } }
            },
            then: {
              required: ['FurnitureDetails']
            },
            else: {
              if: {
                properties: { ListingType: { const: 'HomeAppliance' } }
              },
              then: {
                required: ['HomeApplianceDetails']
              },
              else: {
                if: {
                  properties: { ListingType: { const: 'PhotographyEquipment' } }
                },
                then: {
                  required: ['PhotographyEquipmentDetails']
                }
              }
            }
          }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                ID: { type: 'string', format: 'uuid' },
                CategoryID: { type: 'string', format: 'uuid' },
                UserID: { type: 'string', format: 'uuid' },
                ListingType: {
                  type: 'string',
                  enum: ['Property', 'Vehicle', 'Furniture', 'HomeAppliance', 'PhotographyEquipment']
                },
                Title: { type: 'string' },
                Description: { type: 'string' },
                Location: { type: 'string' },
                Latitude: { type: 'number' },
                Longitude: { type: 'number' },
                PricePerDay: { type: 'number' },
                PricePerWeek: { type: 'number' },
                PricePerMonth: { type: 'number' },
                SecurityDeposit: { type: 'number' },
                Condition: { type: 'string' },
                DeliveryOptions: { type: 'string' },
                PropertyDetails: {
                  type: 'object',
                  properties: {
                    PropertyType: { type: 'string' },
                    Address: { type: 'string' },
                    AreaSize: { type: 'number' },
                    NumberOfBedrooms: { type: 'integer' },
                    NumberOfBathrooms: { type: 'integer' },
                    Furnished: { type: 'boolean' },
                    FloorNumber: { type: 'integer' },
                    ParkingAvailable: { type: 'boolean' },
                    AvailableFrom: { type: 'string', format: 'date-time' },
                    MinimumRentalPeriod: { type: 'integer' }
                  }
                },
                VehicleDetails: {
                  type: 'object',
                  properties: {
                    VehicleType: { type: 'string' },
                    BrandName: { type: 'string' },
                    ModelName: { type: 'string' },
                    RegistrationNumber: { type: 'string' },
                    YearOfManufacture: { type: 'integer', minimum: 1900 },
                    Color: { type: 'string'},
                    NumberOfSeats: { type: 'integer', minimum: 1 }
                  }
                },
                FurnitureDetails: {
                  type: 'object',
                  properties: {
                    FurnitureType: { type: 'string' },
                    Material: { type: 'string' },
                    Size: { type: 'string' },
                    Color: { type: 'string' },
                    Quantity: { type: 'integer' }
                  }
                },
                HomeApplianceDetails: {
                  type: 'object',
                  properties: {
                    ApplianceType: { type: 'string' },
                    BrandName: { type: 'string' },
                    ModelNumber: { type: 'string' },
                    PowerRating: { type: 'string' },
                    WarrantyAvailable: { type: 'boolean' }
                  }
                },
                PhotographyEquipmentDetails: {
                  type: 'object',
                  properties: {
                    EquipmentType: { type: 'string' },
                    BrandModel: { type: 'string' },
                    UsageInstructionsAttached: { type: 'boolean' },
                    AccessoriesIncluded: { type: 'string' },
                    PickupLocation: { type: 'string' }
                  }
                },
                CreatedAt: { type: 'string', format: 'date-time' },
                UpdatedAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            error: { type: 'string' }
          }
        }
      }
    },
    // preHandler: async (request, reply) => {
    //   try {
    //     await request.jwtVerify();
    //   } catch (err) {
    //     reply.send(err);
    //   }
    // },
    handler: listingController.createListing.bind(listingController)
  });

  // Get all listings
  fastify.get('/', {
    schema: {
      tags: ['listings'],
      summary: 'Get all listings with search and filters',
      description: 'Get all listings with optional search, filters and pagination',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          categoryId: { type: 'string', format: 'uuid' },
          subcategoryId: { type: 'string', format: 'uuid' },
          location: { type: 'string' },
          minPrice: { type: 'number', minimum: 0 },
          maxPrice: { type: 'number', minimum: 0 },
          condition: { type: 'string' },
          search: { type: 'string' },
          sortBy: { type: 'string', default: 'CreatedAt' },
          sortOrder: { type: 'string', enum: ['ASC', 'DESC'], default: 'DESC' }
        }
      }
    },
    handler: listingController.getAllListings.bind(listingController)
  });

  // Get listing by ID
  fastify.get('/:id', {
    schema: {
      tags: ['listings'],
      summary: 'Get listing by ID',
      description: 'Get a specific listing by ID',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.getListingById.bind(listingController)
  });

  // Get related listings
  fastify.get('/:id/related', {
    preHandlers: [authHook],
    schema: {
      security: [{ bearerAuth: [] }],
      tags: ['listings'],
      summary: 'Get related listings',
      description: 'Get listings related to a specific listing (same category/subcategory)',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 20, default: 6 }
        }
      }
    },
    handler: listingController.getRelatedListings.bind(listingController)
  });



  // Category routes
  fastify.get('/categories', {
    schema: {
      tags: ['categories'],
      summary: 'Get all categories',
      description: 'Get all categories with subcategories'
    },
    handler: listingController.getAllCategories.bind(listingController)
  });

  // Get category by ID
  fastify.get('/categories/:id', {
    schema: {
      tags: ['categories'],
      summary: 'Get category by ID',
      description: 'Get a specific category with its subcategories',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.getCategoryById.bind(listingController)
  });

  // Update listing
  fastify.put('/:id', {
    preHandlers: [authHook],
    schema: {
      tags: ['listings'],
      security: [{ bearerAuth: [] }],
      summary: 'Update listing',
      description: 'Update a listing by ID',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          CategoryID: { type: 'string', format: 'uuid' },
          SubcategoryID: { type: 'string', format: 'uuid' },
          Title: { type: 'string', minLength: 3, maxLength: 200 },
          Description: { type: 'string', minLength: 10, maxLength: 5000 },
          Location: { type: 'string', minLength: 3, maxLength: 200 },
          Latitude: { type: 'number', minimum: -90, maximum: 90 },
          Longitude: { type: 'number', minimum: -180, maximum: 180 },
          PricePerDay: { type: 'number', minimum: 0 },
          PricePerWeek: { type: 'number', minimum: 0 },
          PricePerMonth: { type: 'number', minimum: 0 },
          SecurityDeposit: { type: 'number', minimum: 0 },
          Condition: {
            type: 'string',
            // properties: {
            //   new: { type: 'boolean' },
            //   good: { type: 'boolean' },
            //   used: { type: 'boolean' }
            // }
          },
          DeliveryOptions: {
            type: 'string',
            // properties: {
            //   pickup: { type: 'boolean' },
            //   homeDelivery: { type: 'boolean' }
            // }
          }
        }
      }
    },
    handler: listingController.updateListing.bind(listingController)
  });

  // Delete listing
  fastify.delete('/:id', {
    preHandlers: [authHook],
    schema: {
      tags: ['listings'],
      summary: 'Delete listing',
      description: 'Delete a listing by ID',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.deleteListing.bind(listingController)
  });

  // Get subcategories by category
  fastify.get('/categories/:id/subcategories', {
    schema: {
      tags: ['categories'],
      summary: 'Get subcategories by category',
      description: 'Get all subcategories for a specific category',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.getSubcategoriesByCategory.bind(listingController)
  });

  // Home page API - Get categories with their data
  fastify.get('/home', {
    schema: {
      tags: ['home'],
      summary: 'Get home page data',
      description: 'Get all categories with sample listings for home page display',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 20, default: 8 }
        }
      }
    },
    handler: listingController.getHomePageData.bind(listingController)
  });

  // Category page API - Get category with subcategories and listings
  fastify.get('/categories/:id/page', {
    schema: {
      tags: ['categories'],
      summary: 'Get category page data',
      description: 'Get category with subcategories and listings (filtered by subcategory if specified)',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          subcategoryId: { type: 'string', format: 'uuid' },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 }
        }
      }
    },
    handler: listingController.getCategoryPageData.bind(listingController)
  });

  // View tracking routes

  // Track a view for a listing
  fastify.post('/:id/view', {
    schema: {
      tags: ['listings'],
      summary: 'Track a view for a listing',
      description: 'Record a unique view for a listing based on user or anonymous session',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          sessionId: { type: 'string', maxLength: 255 },
          referrer: { type: 'string', maxLength: 500 },
          deviceType: {
            type: 'string',
            enum: ['desktop', 'mobile', 'tablet', 'unknown'],
            default: 'unknown'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                alreadyViewed: { type: 'boolean' },
                viewId: { type: 'string' },
                message: { type: 'string' }
              }
            }
          }
        }
      }
    },
    handler: listingController.trackListingView.bind(listingController)
  });

  // Get view statistics for a listing
  fastify.get('/:id/statistics', {
     preHandlers: [authHook],
    schema: {
      tags: ['listings'],
      summary: 'Get view statistics for a listing',
      description: 'Get detailed view statistics including unique users, anonymous views, and recent activity',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          days: { type: 'integer', minimum: 1, maximum: 365, default: 30 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalViews: { type: 'integer' },
                uniqueUsers: { type: 'integer' },
                anonymousViews: { type: 'integer' },
                recentViews: { type: 'integer' },
                period: { type: 'string' }
              }
            }
          }
        }
      }
    },
    handler: listingController.getListingViewStatistics.bind(listingController)
  });

  // Get daily view counts for a listing
  fastify.get('/:id/daily-views', {
    schema: {
      tags: ['listings'],
      summary: 'Get daily view counts for a listing',
      description: 'Get daily view counts for analytics and trending data',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          days: { type: 'integer', minimum: 1, maximum: 90, default: 7 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string' },
                  views: { type: 'integer' }
                }
              }
            }
          }
        }
      }
    },
    handler: listingController.getListingDailyViews.bind(listingController)
  });

  // Get unique view count for a listing
  fastify.get('/:id/unique-views', {
    schema: {
      tags: ['listings'],
      summary: 'Get unique view count for a listing',
      description: 'Get the total number of unique views for a listing',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                listingId: { type: 'string' },
                uniqueViewCount: { type: 'integer' }
              }
            }
          }
        }
      }
    },
    handler: listingController.getListingUniqueViewCount.bind(listingController)
  });
}

module.exports = listingRoutes;




